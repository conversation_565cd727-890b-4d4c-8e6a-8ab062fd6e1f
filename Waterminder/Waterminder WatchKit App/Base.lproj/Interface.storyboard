<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder.WatchKit.Storyboard" version="3.0" toolsVersion="14815" targetRuntime="watchKit" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="NBu-mt-KyX">
    <device id="watch38"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14770"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBWatchKitPlugin" version="14621"/>
    </dependencies>
    <scenes>
        <!--Hosting Controller-->
        <scene sceneID="RUd-og-cv4">
            <objects>
                <hostingController id="NBu-mt-KyX" customClass="HostingController" 
                customModuleProvider="target"/>                
            </objects>
            <point key="canvasLocation" x="187" y="141"/>
        </scene>
        <!--Static Notification Interface Controller-->
        <scene sceneID="AEw-b0-oYE">
            <objects>
                <notificationController id="YCC-NB-fut">
                    <items>
                        <label alignment="left" text="Alert Label" numberOfLines="0" id="IdU-wH-bcW"/>
                    </items>
                    <notificationCategory key="notificationCategory" identifier="myCategory" id="JfB-70-Muf"/>
                    <connections>
                        <outlet property="notificationAlertLabel" destination="IdU-wH-bcW" id="JKC-fr-R95"/>
                        <segue destination="eXb-UN-Cd0" kind="relationship" relationship="dynamicInteractiveNotificationInterface" id="mpB-YA-K8N"/>
                    </connections>
                </notificationController>
            </objects>
            <point key="canvasLocation" x="187" y="462"/>
        </scene>
        <!--Notification Hosting Controller-->
        <scene sceneID="Niz-AI-uX2">
            <objects>
                <controller id="eXb-UN-Cd0" customClass="NotificationController"
                customModuleProvider="target"/>
            </objects>
            <point key="canvasLocation" x="488" y="462"/>
        </scene>
    </scenes>
</document>
